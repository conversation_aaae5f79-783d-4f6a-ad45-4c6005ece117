import { FormControl } from '@angular/forms';
import { Nullable } from 'se-ui-components-mf-lib';

export interface TaxSubGroupControls {
  baseImposable: FormControl<number | null>;
  reduccio: FormControl<number | null>;
  baseLiquidable: FormControl<number | null>;
  tipusGravament: FormControl<number | null>;
  quotaIntegra: FormControl<number | null>;
}

export type TaxSubGroupKeys =
  | 'dioxidSofre'
  | 'oxidNitrogen'
  | 'particules'
  | 'carboniOrganic';

export type CodiGravamen = 'DIOAZU' | 'OXINIT' | 'PARTIC' | 'CARORG';

export interface GravamentTaxSubGroup {
  codiGravamen: CodiGravamen;
  importTarifa: number;
}
export interface ReduccioBonificacio {
  codi: CodiGravamen;
  valor: number;
  tipus: 'REDUCCIONS' | 'BONIFICACIONS';
}

interface Contaminant {
  baseImposable: number;
  reduccio: number;
  baseLiquidable: number;
  tipusGravament: number;
  quotaIntegra: number;
}

export interface TaxDeclararion {
  idTramit: string;
  dioxidSofre: Contaminant;
  oxidNitrogen: Contaminant;
  particules: Contaminant;
  carboniOrganic: Contaminant;
  sumaQuotes: number;
  anex: Anex;
  indBonificacioCombustible: boolean;
  quotaIntegraBonificable: Nullable<number>;
  bonificacioPercentCombustible: Nullable<number>;
  bonificacioPercentAplicable: Nullable<number>;
  totalBonificacioCombustible: Nullable<number>;
  indBonificacioInversio: boolean;
  inversioRealizada: Nullable<number>;
  bonificacioPercentInversio: Nullable<number>;
  bonificacioInversioResultat: Nullable<number>;
  totalBonificacioInversio: Nullable<number>;
}

export interface Anex {
  idPadoct: string;
  nom: string;
  pes: string;
  descripcio: string;
  tipusDocument: string;
  extension: string;
}

export interface ContestableActDocument {
  type: string;
  subtype?: string;
  name?: string;
  description?: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}
