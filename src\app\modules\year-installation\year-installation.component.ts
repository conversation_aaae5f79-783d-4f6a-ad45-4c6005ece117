import { Component, OnInit, type OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  take,
  takeUntil,
} from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { Nullable, SeModal, SeModalService } from 'se-ui-components-mf-lib';

import { SearchComplementaryRequestParameters } from './model/year-installation.model';
import { YearInstallationEndpointService } from './year-installation-endpoint.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HeaderInfoService, StoreService } from '@core/services';
import { Constant } from '@core/models/constants.enum';
import { AppRoutes } from '@core/models/app-routes.enum';
import {
  SelfAssessment,
  SelfAssessmentStatusEnum,
} from '@core/models/self-assessment-status.model';

@Component({
  selector: 'app-year-installation',
  templateUrl: './year-installation.component.html',
  styleUrls: ['./year-installation.component.scss'],
})
export class YearInstallationComponent implements OnInit, OnDestroy {
  protected readonly IMPOST = Constant.NAME;
  protected readonly removeComplementaryTableColumns = ['tipus'];
  protected readonly EMAIL = '<EMAIL>';

  //Form
  protected componentForm: FormGroup | undefined;

  // Variable para el ejercicio
  protected yearChange: Nullable<number>;

  /* Autoliquidación Complementaria */
  protected dadesInitialsComplementary = false; // Indica si se han cargado datos iniciales con una autoliquidación complementaria
  protected isComplementarySearchComponentVisible = false;
  protected searchComplementaryRequestParams:
    | SearchComplementaryRequestParameters
    | undefined;

  private continueWithoutSearch: boolean | undefined = undefined;
  private selfAssessmentsFound: boolean = false;
  protected showYearPeriodCustomElement: boolean = false; // Cargamos el componente de ejercicio una vez consultado dades-iniciales
  private idTramit: Nullable<string> = null;
  private _beforeIdTramit: Nullable<string> = null; // Working session previous idTramit
  // Datos recuperados de dades-inicials
  private hasInitialData: boolean = false;
  // Ocultar complementaria en caso de recuperar la sesión de trabajo de una complementaria
  private hideComplementary = false;
  protected panelTheme: { theme: string; colapsible: boolean } = {
    theme: 'default',
    colapsible: false,
  };
  private destroyed$ = new Subject<void>();

  constructor(
    private router: Router,
    protected store: StoreService,
    private header: HeaderInfoService,
    private yearInstallationSrv: YearInstallationEndpointService,
    private translateService: TranslateService,
    private modalService: SeModalService,
    private fb: FormBuilder,
  ) {
    this.header.resetYearInstallation();
  }

  ngOnInit(): void {
    this.idTramit = this.store.idTramit;
    if (this.idTramit) {
      this.fillComplementaryParameters();
      this.createForm(this.idTramit);
      this.getInitialData(this.idTramit);
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private createForm(idTramit: string): void {
    this.componentForm = this.fb.group({
      // static
      idTramit: [idTramit, Validators.required],
      model: [Constant.MODEL, Validators.required],
      // editable
      exercici: [null, Validators.required],
      codEmis: [
        null,
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(5),
          Validators.pattern(/^[0-9]+$/),
        ],
      ],

      // Complementaria (dynamic)
      numJustificantComplementari: [null],
      quotaLiquidaComplementari: [null, Validators.min(0)],
      dataPresentacioComplementari: [null],
    });

    this.componentForm
      .get('codEmis')
      ?.valueChanges.pipe(
        takeUntil(this.destroyed$),
        distinctUntilChanged(),
        debounceTime(700),
      )
      .subscribe((newCode: string) => {
        if (
          newCode &&
          newCode.length >= 2 &&
          newCode.length <= 5 &&
          /^[0-9]+$/.test(newCode) &&
          this.componentForm?.get('exercici')?.value
        ) {
          this.verifyWorkingSessionAndComplementary();
        }
      });
  }

  protected isComplementarySearchOptional(): boolean {
    const currentYear = this.componentForm?.get('exercici')?.value;
    return currentYear && +currentYear < 2025;
  }

  protected onYearChange(event: Event): void {
    const customEvent = event as CustomEvent<{
      year: number;
    }>;

    // cuando se selecciona un año desde dades iniciales hay que actualizar el componente
    if (this.hasInitialData) {
      this.hasInitialData = false;
      return;
    }

    if (this.store.taxYear) {
      this.yearChange = this.store.taxYear;
      this.store.taxYear = null;
    }

    if (customEvent.detail) {
      const { year } = customEvent.detail;

      if (year) {
        this.componentForm?.patchValue({
          exercici: `${year}`,
        });
      }
    }
    this.verifyWorkingSessionAndComplementary();
  }

  private verifyWorkingSessionAndComplementary(): void {
    // Si no hemos encontrado una autoliquidación complementaria al cargar los datos iniciales, limpiamos los datos de la complementaria
    // y buscamos si hay una sesión de trabajo y/o complementaria.
    if (!this.dadesInitialsComplementary) {
      this.cleanComplementaryData();
      this.verifyIfHaveWorkingSession();
    } else {
      this.dadesInitialsComplementary = false;
    }
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }

  protected onContinueWithoutSearch(event: Event): void {
    this.continueWithoutSearch = !(event as CustomEvent).detail;
  }

  protected submit(): void {
    if (this.componentForm?.valid) {
      this.yearInstallationSrv
        .saveInitialData(this.componentForm.getRawValue())
        .pipe(takeUntil(this.destroyed$))
        .subscribe((response) => {
          if (response) {
            const exercici = this.componentForm?.get('exercici')?.value ?? '';
            const codEmis = this.componentForm?.get('codEmis')?.value ?? '';
            const numJustificantComplementari =
              this.componentForm?.get('numJustificantComplementari')?.value ??
              '';

            this.store.taxYear = +exercici;
            this.header.taxYear = +exercici;

            this.store.codEmis = codEmis;
            this.header.codEmis = codEmis;

            this.header.model = Constant.MODEL;
            this.header.receiptId = numJustificantComplementari;
            this.store.numJustificantComplementari =
              numJustificantComplementari;
            this.header.status = SelfAssessmentStatusEnum.IN_PROGRESS;

            this.router.navigate([AppRoutes.TAX_DECLARATION]);
          }
        });
    }
  }

  protected get isButtonEnabled(): boolean {
    return (
      (!this.isComplementarySearchComponentVisible ||
        (this.isComplementarySearchComponentVisible &&
          !this.selfAssessmentsFound &&
          (this.continueWithoutSearch === true ||
            this.continueWithoutSearch === undefined))) &&
      !!this.componentForm?.valid
    );
  }

  protected submitComplementary(event: Event): void {
    const { detail } = event as CustomEvent<Nullable<SelfAssessment>>;

    if (!detail) return;

    const { dataPresentacio, numJustificant, quotaTributaria } = detail;

    this.componentForm?.patchValue({
      numJustificantComplementari: numJustificant,
      quotaLiquidaComplementari: quotaTributaria,
      dataPresentacioComplementari: dataPresentacio,
    });

    this.submit();
  }

  protected onPayButtonClick(event: Event): void {
    const { detail } = event as CustomEvent<SelfAssessment>;

    if (detail?.idAutoliquidacio) {
      this.store.amountToPay = detail?.quotaLiquida;
      this.store.selfAssessmentId = detail.idAutoliquidacio;
      this.store.taxYear = this.componentForm?.get('exercici')?.value;
      this.router.navigate([AppRoutes.PAGAMENT]);
    }
  }

  protected onAutomaticComplementarySearchEnd(event: Event): void {
    const { detail } = event as CustomEvent<{
      selfassessments: SelfAssessment[];
      hiddenSelfassessmentsExist: boolean;
    }>;
    const { selfassessments, hiddenSelfassessmentsExist } = detail;

    this.selfAssessmentsFound =
      selfassessments.length > 0 || hiddenSelfassessmentsExist;
    this.isComplementarySearchComponentVisible = true;
    if (selfassessments?.length === 0 && !hiddenSelfassessmentsExist) {
      this.cleanComplementaryData();
    } else if (this.hideComplementary && selfassessments?.length > 0) {
      this.continueWithoutSearch = false;
      // Hemos recuperado una sesión de trabajo de una autoliquidación complementaria
      const { numJustificant, quotaLiquida, dataPresentacio } =
        detail.selfassessments[0];

      this.componentForm?.patchValue({
        numJustificantComplementari: numJustificant,
        quotaLiquidaComplementari: quotaLiquida,
        dataPresentacioComplementari: dataPresentacio,
      });

      this.hideComplementary = false;
      this.isComplementarySearchComponentVisible = false;
    }
  }

  private verifyIfHaveWorkingSession(): void {
    const selectedYear = +this.componentForm?.get('exercici')?.value;
    const clau = this.getClauCode(selectedYear);
    if (selectedYear && clau) {
      this.yearInstallationSrv
        .getWorkingSession(this.idTramit, selectedYear, clau)
        .pipe(takeUntil(this.destroyed$))
        .subscribe((response) => {
          if (response?.content && this.store.hasAppearedWorkingSession) {
            this._beforeIdTramit = response.content; // save id previous working session
            this.openWorkingSessionWarningModal();
          } else if (
            // buscar complementaria cuando no hay sesion de trabajo y no es ya una complementaria
            !this.componentForm?.get('numJustificantComplementari')?.value
          ) {
            this.searchComplementaryData(this.idTramit as string);
          }
        });
    }
  }

  private cleanComplementaryData(): void {
    this.componentForm?.patchValue({
      numJustificantComplementari: null,
      quotaLiquidaComplementari: null,
      dataPresentacioComplementari: null,
    });
  }

  private getInitialData(idTramit: string): void {
    this.yearInstallationSrv
      .getInitialData(idTramit)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        if (response?.content) {
          if (response.content.exercici) {
            this.componentForm
              ?.get('exercici')
              ?.setValue(response.content.exercici);
            if (response.content.codEmis) {
              this.componentForm
                ?.get('codEmis')
                ?.setValue(response.content.codEmis);
            }
            this.hasInitialData = true;
            this.yearChange = +response.content.exercici;
          }
          if (response.content?.numJustificantComplementari) {
            this.componentForm?.patchValue({
              numJustificantComplementari:
                response.content.numJustificantComplementari,
              quotaLiquidaComplementari:
                response.content.quotaLiquidaComplementari,
              dataPresentacioComplementari:
                response.content.dataPresentacioComplementari,
            });
            this.dadesInitialsComplementary = true;
          }
          this.showYearPeriodCustomElement = true;
        }
      });
  }

  private openWorkingSessionWarningModal(): void {
    const data: SeModal = {
      title: this.translateService.instant(
        'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.WORKING_SESSION.MODAL.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.WORKING_SESSION.MODAL.DETAIL',
      ),
      closable: true,
      closableLabel: this.translateService.instant(
        'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.WORKING_SESSION.MODAL.CANCEL_BUTTON',
      ),
      secondaryButton: true,
      secondaryButtonLabel: this.translateService.instant(
        'SE_GASOS_MF.MODULE_YEAR_INSTALLATION.WORKING_SESSION.MODAL.ACCEPT_BUTTON',
      ),
    };

    const modalRef = this.modalService.openModal(data);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.searchComplementaryData(this.idTramit!);
        modalRef.close();
      });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        // Restore previous working session
        if (this._beforeIdTramit) {
          this.idTramit = this._beforeIdTramit;
          this.store.idTramit = this._beforeIdTramit;
          this.componentForm?.get('idTramit')?.setValue(this._beforeIdTramit);
        }
        this.searchComplementaryData(this._beforeIdTramit!);
        this.hideComplementary = true;
        modalRef.close();
      });
  }

  private getClauCode(selectedYear: number): string | null {
    const selectedTaxpayer = this.store.selectedTaxpayer;
    const codEmis = this.componentForm?.get('codEmis')?.value;
    if (!selectedTaxpayer?.nif || !selectedYear || !codEmis) {
      return null;
    }

    return `${selectedYear}_${selectedTaxpayer.nif}_${codEmis}`;
  }

  private fillComplementaryParameters(): void {
    this.searchComplementaryRequestParams = {
      idTramit: this.idTramit!,
      impost: Constant.NAME,
      exercici: '',
      periodi: '',
      model: Constant.MODEL,
      clau: '',
    };
  }

  private searchComplementaryData(idTramit: string): void {
    const clau: string | null = this.getClauCode(
      +this.componentForm?.get('exercici')?.value,
    );

    this.searchComplementaryRequestParams = {
      idTramit,
      impost: Constant.NAME,
      clau: clau ?? '',
      periodi: Constant.ANUAL_PERIOD,
      exercici: `${this.componentForm?.get('exercici')?.value}`,
      model: Constant.MODEL,
    } as SearchComplementaryRequestParameters;

    this.isComplementarySearchComponentVisible = !!clau;
  }
}
