import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Column } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class TaxDeclarationService {
  constructor(private translateService: TranslateService) {
    // Empty constructor
  }

  getLinkDocument(): string {
    return this.translateService.instant(
      'SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.DESCRIPTION',
      {
        LINK: `https://atc.gencat.cat/${this.translateService.currentLang}/tributs/impost-emissions-industria/tramit-emissions-industria/index.html?moda=1&detallId=0042f9db-b798-11e6-ab26-000c29052e2c`,
      },
    );
  }

  getTableColumns(): Column[] {
    return [
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
        key: 'name',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
        cellConfig: {
          tooltip: true,
          ellipsis: true,
        },
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
        key: 'size',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
        key: 'docType',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
        key: 'description',
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
    ];
  }

  getModalTableColumns(): Column[] {
    return [
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
        key: 'name',
        cellComponentName: 'defaultCellComponent',
        cellConfig: {
          tooltip: true,
          ellipsis: true,
        },
        resizable: false,
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
        key: 'size',
        size: 10,
        cellComponentName: 'defaultCellComponent',
        resizable: false,
      },
      {
        header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
        key: 'docType',
        cellComponentName: 'dropdownCellComponent',
        resizable: false,
        cellConfig: {
          id: 'docType',
          options: [
            {
              id: 1,
              label: this.translateService.instant(
                'SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.NOM_DOCUMENT',
              ),
            },
          ],
          disabled: true,
          showClear: false,
          editable: false,
          autoSelect: true,
          readOnly: false,
          filter: false,
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: true,
        },
      },
      {
        header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
        key: 'description',
        cellComponentName: 'inputCellComponent',
        resizable: false,
        tooltip: true,
        tooltipText: this.translateService.instant(
          'SE_GASOS_MF.MODULE_TAX_DECLARATION.DOCUMENTS.TOOLTIP_DESCRIPTION',
        ),
        cellConfig: {
          id: 'docDescription',
          optionLabel: 'label',
          optionValue: 'id',
          placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
          required: true,
        },
      },
    ];
  }
}
